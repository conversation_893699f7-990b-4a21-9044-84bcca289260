// hsr730akqp3415i1cv7a3ebjes

// 985---https://search.weixin.qq.com/cgi-bin/newsearchweb/userclientjump?path=page/search/christmas_jump&query=http%3A%2F%2Fbx65qqd08281407.lvxji78gp.cn%2Fnewt%2Fnew.html%3F4fc%3Duma%26ch%3Dbx65qqd%26upuid%3D8145216%26z1n%3Dcei


//帆帆 8196013  
//帆帆 8196230 iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2
//薯条 8199733 iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2 
//青青 8201579 iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2 

const EncryptToIu = (key) => {
    const base64_str = Buffer.from(key).toString("base64").replace(/=/g, "")
    return `iu${base64_str}2`
}

console.log(EncryptToIu('bx65qqd'))


// 给你3组数据，尝试解密从数字到字符串的加密方法：
// 8196230 iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2
// 8199733 iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2
// 8201579 iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2

// 可能用到的数据：
// ch=bx65qqd hqs=fdj upuid=8196926 xtp=0kp