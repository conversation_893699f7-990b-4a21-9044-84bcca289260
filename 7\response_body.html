<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="data:image/ico;base64,aWNv">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://dtbwjl.oss-cn-beijing.aliyuncs.com/static/app/js/zepto1.2.min.js"></script>
    <style>
        :root{
            --main-color: #79dea1;
        }
        body, html {
            background-color: var(--main-color);
        }

        .rrp-content {
            text-align: center;
            font-family: "微软雅黑";
            padding: 5vw;
            position: relative;
            z-index: 10;
        }

        .rrp-top{
            color: #fff;
            text-align: center;
            font-weight: bolder;
            font-size: 7vw;
        }

        .rrp-info-box{
            background-color: #fff;
            border-radius: 5vw;
            display: flex;
            border-bottom: 1px solid #eee;
            padding: 2vw 0;
        }

        .rrp-info-box-item{
            flex: 1;
            text-align: center;
            font-size: 4vw;
            font-weight: bolder;
            line-height: 10vw;
        }

        .rrp-box1-title {
            color: #fff;
            font-weight: bolder;
            font-size: 4.5vw;
            margin-bottom: 3vw;
        }

        .rrp-box1-title span {
            color: #f17171;
            margin: 0 2vw;
        }

        .rrp-box2 {
            background-color: #fff;
            padding: 4vw 10vw 7vw;
            border-radius: 5vw;
        }

        .rrp-box2-tips {
            color: var(--main-color);
            font-weight: bolder;
            margin-top: 3vw;
            font-size: 4vw;
        }

        .rrp-box1-left-times {
            color: #fff;
            font-size: 4vw;
            margin: 4vw 0;
        }

        .rrp-box1-get-packets {
            background-color: #fff;
            color: var(--main-color);
            text-align: center;
            line-height: 30vw;
            width: 30vw;
            height: 30vw;
            border-radius: 50%;
            margin: 10vw auto;
            font-size: 6vw;
        }
        .rrp-box1-get-packets{
            animation:rotate2 .8s linear 0s infinite;
        }

        @keyframes rotate2 {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1)
            }
            50% {
                -webkit-transform: scale(1.1);
                transform: scale(1.1)
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1)
            }
        }
        .index-qrCodeBlock p{
            font-size: 5vw;
            font-weight: bolder;
            text-align: center;
        }

        .posterMask {
            width: 100vw;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .index-qrcodeimg {
            position: absolute;
            top: 0;
            width: 100%;
            left: 0;
            z-index: 100;
        }

        .index-qrcode {
            display: none;
        }

        .index-codeimg {
            width: 100%;
        }

        .index-qrCodeBlock {
            width: 70vw;
            padding: 3vw 7vw 5vw;
            background-color: #fff;
            border-radius: 2vw;
        }
        .index-qrCodeBlock p{
            font-size: 5vw;
            font-weight: bolder;
            text-align: center;
        }
        .button {
            background-color: var(--main-color);
            height: 10vw;
            line-height: 10vw;
            text-align: center;
            border-radius: 5vw;
            color: #fff;
        }
    </style>
</head>
<body>
<div class="rrp-top">
    推荐读书
</div>
<div class="rrp-content">
    <div id="tip_text" class="rrp-box1-title">每天<span id="one_times"></span>轮，每轮<span id="one_num"></span>篇，每小时1轮</div>
    <div class="rrp-info-box">
        <div class="rrp-info-box-item">
            <div id="total_num" class="rrp-info-box-item-b">0</div>
            <div class="rrp-info-box-item-t">剩余可读</div>
        </div>
        <div class="rrp-info-box-item">
            <div id="finish_num" class="rrp-info-box-item-b">0</div>
            <div class="rrp-info-box-item-t">今日已读</div>
        </div>
    </div>
    <div style="margin-top: 3vw" id="not_finish" class="rrp-box1-left-times">还差<span id="left_num"></span>篇完成本轮</div>
    <div style="margin-top: 3vw" id="is_finish" class="rrp-box1-left-times">距离下一轮还剩：<span id="left_time"></span>分</div>

    <div class="rrp-box1-left-times">点击开始后，点返回推荐下一篇</div>
    <div onclick="show_qrcode()" class="rrp-box1-get-packets">开始</div>
    <div class="rrp-box1-left-times">用户ID：<span id="uid"></span>【<span id="channel"></span>】</div>
    <div class="rrp-box2">
        <div class="rrp-box2-tips">友情警示</div>
        <div class="rrp-box2-tips">请不要转账付款</div>
        <div class="rrp-box2-tips">防止上当受骗哦</div>
    </div>
    <div onclick="close_mask(this)" id="posterMask" class="posterMask">
        <div id="qrCodeBlock" class="index-qrCodeBlock">
            <!--                <p style="color: var(&#45;&#45;main-color)">请复制链接</p>-->
            <!--                <p style="color: var(&#45;&#45;main-color)">发送到微信聊天窗口</p>-->
            <!--                <p style="color: var(&#45;&#45;main-color)">打开链接进行阅读</p>-->
            <!--                <p style="word-break:break-all; font-size: 4vw;" id="read_url">https://aappii.hufenaa.cn/read_channel2/get_read_url?iu=</p>-->
            <!--                <div style="font-weight: bolder;font-size: 6vw;" onclick="do_copy()" class="button" id="zs_doCopyPromoteTxt">复制</div>-->
            <!--                <p style="background-color: #f7f7f7; color: #333; font-size:4vw;text-align: center;font-weight: bolder;    margin-top: 4vw;border-radius: 2vw;padding: 2vw 5vw;border-bottom: 1px solid var(&#45;&#45;main-color)">(临时阅读链接，10分钟后失效，每次阅读要重新获取)</p>-->
            <div id="qrcode" class="index-qrcode"></div>
            <img class="index-codeimg" id="codeimg">
            <!--                <p style="font-size:5vw;margin-top:10vw;text-align: center;font-weight: bolder;">长按二维码开始阅读</p>-->
            <p style="font-size:4vw;text-align: center;font-weight: bolder;">(临时二维码，请勿截图使用)</p>
            <p style="font-size:4vw;color: var(--main-color)">长按二维码,点击“访问网页”开始阅读</p>
            <img style="width: 70%;" src="https://yedt.oss-cn-beijing.aliyuncs.com/tips.png" alt="">
        </div>
    </div>
</div>
<script src="https://bwjl-app.oss-cn-beijing.aliyuncs.com/static/app/js/qrcode.js?aa"></script>
<script>
    var param = getParam();
    var iu = localStorage.getItem('iu');

    if(param.special_user_key){
        sessionStorage.setItem('special_user_key',param.special_user_key);
    }

    if(param.ch && (param.ch == 'ba01' || param.ch == 'ba02')){
        $('.rrp-content').hide();
    }

    if(!iu){
        do_login();
    }else{
        ggginfo();
    }

    var can_show = true;
    var is_xcx = false;
    function do_login(){
        if(param.iu){
            iu = param.iu;
            localStorage.setItem('iu',param.iu);
            ggginfo();return;
        }
        if(param.code){
            $.get('https://aappii.hufenaa.cn/user/login3?code='+param.code+'&state='+param.state,function(res){
                if(res.code == 0){
                    iu = res.data.iu;
                    localStorage.setItem('iu',iu);
                    ggginfo();
                }else{
                    alert(res.msg);
                }
            })
        }else{
            $.get('https://aappii.hufenaa.cn/user/get_login_url'+location.search,function(res){
                location.href = res.data.url
            })
        }
    }

    function ggginfo(){
        var url = 'https://aappii.hufenaa.cn/read_channel2?iu='+iu+'&ch='+param.ch;
        var special_user_key = sessionStorage.getItem('special_user_key')
        if(special_user_key){
            url += '&special_user_key='+special_user_key;
        }
        $.get(url,function(res){
            if(res.code == 401){
                localStorage.removeItem('iu');
                do_login();return;
            }
            if(res.code != 0){
                $('.rrp-box1-get-packets').css('background-color','#ccc')
                can_show = false;
                alert(res.msg);return;
            }
            if(res.data.url){
                location.href = res.data.url;return;
            }
            can_show = true;
            is_xcx = res.data.is_xcx;
            $('#total_num').html(res.data.total_num);
            $('#finish_num').html(res.data.finish_num);
            $('#one_times').html(res.data.one_times);
            $('#one_num').html(res.data.one_num);
            $('#left_num').html(res.data.left_num);
            $('#channel').html(res.data.channel);
            $('#uid').html(res.data.uid);
            if(res.data.left_time <= 0){
                $('#not_finish').show();
                $('#is_finish').hide();
            }else{
                $('#not_finish').hide();
                $('#is_finish').show();
                make_time(res.data.left_time);
            }
        })
    }
    var time = 0;
    function make_time(left_minute){
        time = left_minute * 60;
        var timer = setInterval(function(){
            time --;
            let str = Math.floor(time / 60) + ":" + (time %60);
            $('#left_time').text(str);
            if(time <= 0){
                $('#not_finish').show();
                $('#is_finish').hide();
                clearInterval(timer);
            }
        },1000)
    }

    function jumper(url){
        top.location.href = url;
    }
    var rp = '';
    function gddeogg(){
        history.pushState(history.length+1, "message", window.location.href.split('#')[0]+"#"+new Date().getTime());
        if(navigator.userAgent.indexOf('Android') != -1){
            if(typeof(tbsJs) != "undefined"){
                tbsJs.onReady('{useCachedApi : "true"}', function(e) {});
                window.onhashchange=function(){
                    jumper(rp);
                };
            }else{
                var pop = 0;
                window.onhashchange = function(event) {
                    pop++;
                    if (pop >= 3) {
                        jumper(rp);
                    }else{
                        history.go(1);
                    }
                };
                history.go(-1);
            }
        }else{
            window.onhashchange=function(){
                jumper(rp);
            };
        }
    }
    gddeogg();
    function getParam(){
        var res = location.search.substring(1).split('&');

        var items = {};
        for(var i=0;i<res.length;i++){
            var a = res[i].split('=');
            items[a[0]] = a[1];
        }
        return items
    }
    function close_mask(e){
        if(event.target.className == 'posterMask'){
            $('#posterMask').hide();
            ggginfo();
        }
    }
    var is_make_qrcode = false;
    function show_qrcode(){
        if(!can_show){
            return;
        }
        $('#posterMask').css('display','flex');
        if(!is_make_qrcode){
            var url ="https://aappii.hufenaa.cn/read_channel2/get_read_url?iu="+iu+'&ch='+param.ch;
            var special_user_key = sessionStorage.getItem('special_user_key');
            if(special_user_key){
                url += '&special_user_key='+special_user_key;
            }
            $.get(url,function(res){
                var url = res.jump;
                if(is_xcx){
                    location.href = url;
                }else{
                    // $('#read_url').html(url);
                    // $('#posterMask').css('display','flex');
                    var qrcode = new QRCode(document.querySelector('#qrcode'), {
                        text: url, // 需要转换为二维码的内容
                        width: 100,
                        height: 100,
                        colorDark: '#000000',
                        colorLight: '#ffffff',
                        correctLevel: QRCode.CorrectLevel.Q
                    })
                    let qrcodeEle = document.getElementById("qrcode")
                    let cvs = qrcodeEle.querySelector('canvas');
                    is_make_qrcode = true;
                    document.querySelector('#codeimg').src = cvs.toDataURL();
                }
            });
        }
    }
    function send_sms() {
        if (timer) return;
        var mobile = $('#mobile').val();
        var reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (!reg.test(mobile)) {
            $fqkk.toast_error('您输入的手机号有误');
            return
        }
        $.post('/user/bind_send_sms', {mobile: mobile}, function (res) {
            if (res.code == 0) {
                $fqkk.toast_success('发送成功，请注意查收');
                settimer();
            } else {
                if (!res.msg) {
                    $fqkk.toast_error('未知错误');
                } else {
                    $fqkk.toast_error(res.msg);
                }
            }
        })
    }

    function settimer() {
        $('.perfect-send-btn').addClass('perfect-send-btn-disabled').text('60s');
        sec = 60;
        timer = setInterval(function () {
            sec--;
            if (sec <= 0) {
                $('.perfect-send-btn').removeClass('perfect-send-btn-disabled').html('立即获取');
                clearInterval(timer);
                timer = null;
                return;
            }
            $('.perfect-send-btn').text(sec + 's');
        }, 1000);
    }

    function submit_sms() {
        var data = {
            mobile: $('#mobile').val(),
            code: $('#code').val(),
        }
        if (!data.code || data.code.length < 6) {
            $fqkk.toast_error('请填写正确的验证码');
            return
        }
        var reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (!reg.test(data.mobile)) {
            $fqkk.toast_error('您输入的手机号有误');
            return
        }
        $.post('', data, function (res) {
            console.log(res)
            if (res.code == 0) {
                $fqkk.toast_success(res.msg);
                setTimeout(function () {
                    history.go(-1)
                }, 2000)
            } else {
                $fqkk.toast_error(res.msg);
            }
        })
    }
    function do_copy(){
        copy($('#read_url').text()).then(function(){
            alert('复制成功，请粘贴到微信窗口阅读')
        })
    }
    function copy(value){
        return new Promise(( resolve, reject ) => {
            if(!value){alert('复制失败，请重试')}
            const textarea = document.createElement('textarea');
// 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
            textarea.readOnly = 'readonly';
            textarea.style.position = 'absolute';
            textarea.style.top = '0px';
            textarea.style.left = '-9999px';
            textarea.style.zIndex = '-9999';
// 将要 copy 的值赋给 textarea 标签的 value 属性
            textarea.value = value
// 将 textarea 插入到 el 中
            const el = document.querySelector('body');
            el.appendChild(textarea);
// 兼容IOS 没有 select() 方法
            if (textarea.createTextRange) {
                textarea.select(); // 选中值并复制
            } else {
                textarea.setSelectionRange(0, value.length);
                textarea.focus();
            }
            const result = document.execCommand('Copy');
            if (result) resolve()
            el.removeChild(textarea);
        })
    }
</script>
<script>
    document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
        WeixinJSBridge.call('hideOptionMenu');
    });
</script>
</body>
</html>